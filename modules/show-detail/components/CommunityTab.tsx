import { useGetCommentsInfiniteQuery } from '@/apis/comment/queries';
import { ICommentCommunity } from '@/apis/comment/types';
import CommentEpisode from '@/modules/episode-detail/components/Comment';
import { memo, useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import Comment from './Comment';
import { IconLoading } from '@/components/IconLoading';
import { useLocalSearchParams } from 'expo-router';
import { FlatListAnimateProps } from '@/components/FlatListAnimate';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Spacer } from '@/components/Spacer';

const CommunityTab = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const podcastId = localParams['podcastId'] as string;

  const {
    data: podcastComments,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetching,
  } = useGetCommentsInfiniteQuery({
    podcastId,
    page: 1,
    limit: 20,
  });

  const pages = podcastComments?.pages ?? [];
  const comments =
    pages?.flatMap((page) =>
      page.data.map((item) => ({ ...item, page: page.meta.currentPage, limit: page.meta.itemsPerPage }))
    ) ?? [];

  const renderItem = useCallback<FlatListAnimateProps<ICommentCommunity>['renderItem']>(({ item }) => {
    if (item.type === 'podcast') {
      return <Comment key={item.id} comment={item} podcastId={item.parentId} />;
    }

    return <CommentEpisode key={item.id} comment={item} episodeId={item.parentId} />;
  }, []);

  const keyExtractor = useCallback<(item: ICommentCommunity, index: number) => string>(
    (item) => `${item.id}_${item.source}_${item.type}`,
    []
  );

  const onEndReached = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderSkeletonComponent = useCallback(() => {
    return <IconLoading />;
  }, []);

  return (
    <TabFlashList
      data={comments}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      style={styles.container}
      contentContainerStyle={styles.commentsContainer}
      ListHeaderComponent={<Spacer height={24} />}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={onEndReached}
      onEndReachedThreshold={5}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
      ListFooterComponent={isFetching || isFetchingNextPage ? renderSkeletonComponent : null}
      ListFooterComponentStyle={styles.skeletonContainer}
      estimatedItemSize={120}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  userIcon: {
    marginRight: 16,
  },
  commentsContainer: {
    paddingLeft: 24,
    paddingBottom: 48,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    marginVertical: 24,
    marginRight: 24,
  },
  skeletonContainer: {
    width: '100%',
    gap: 12,
  },
}));

export default memo(CommunityTab);
