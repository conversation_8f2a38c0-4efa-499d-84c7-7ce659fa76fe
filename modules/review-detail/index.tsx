import { useGetProfileQuery } from '@/apis/auth/queries';
import {
  useGetCommentByIdQuery,
  useGetEpisodeCommentByIdQuery,
  useGetInfiniteCommentEpisodeReplyListQuery,
  useGetInfiniteCommentReplyListQuery,
} from '@/apis/comment/queries';
import { useGetEpisodeByIdQuery, useGetEpisodeRateByIdQuery } from '@/apis/episode/queries';
import { ISource } from '@/apis/params';
import { useGetPodcastByIdQuery, useGetPodcastRateByPodcastIdQuery } from '@/apis/podcast';
import { FlatListAnimate, FlatListAnimateProps } from '@/components/FlatListAnimate';
import { Header } from '@/components/ui/Header';
import { ReplyComment } from '@/components/ui/ReplyComment';
import queryKeys from '@/utils/queryKeys';
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Comment } from './components/Comment';
import { CommentSkeleton } from './components/CommentSkeleton';
import { PostHeader } from './components/PostHeader';
import { useCallback } from 'react';
import { ICommentReply } from '@/apis/comment/types';
import { Divider } from '@/components/Divider';

export const ReviewDetail = () => {
  const { data: userProfile } = useGetProfileQuery();
  const { podcastId, episodeId, postId, source } = useLocalSearchParams<{
    postId: string;
    podcastId: string;
    episodeId: string;
    source: ISource;
  }>();
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  // Determine if this is for podcast or episode
  const isForPodcast = !!podcastId;
  const isForEpisode = !!episodeId;

  // Use appropriate comment query based on type
  const { data: podcastPost, isPending: isPendingPodcastPost } = useGetCommentByIdQuery(postId, source, {
    enabled: isForPodcast,
  });
  const { data: episodePost, isPending: isPendingEpisodePost } = useGetEpisodeCommentByIdQuery(postId, source, {
    enabled: isForEpisode,
  });

  // Use the appropriate post data
  const post = isForPodcast ? podcastPost : episodePost;
  const isPendingPost = isForPodcast ? isPendingPodcastPost : isPendingEpisodePost;

  const { data: podcastRate } = useGetPodcastRateByPodcastIdQuery(
    { podcastId },
    { enabled: !!podcastId, refetchOnMount: true }
  );
  const { data: episodeRate } = useGetEpisodeRateByIdQuery(episodeId);
  const { data: podcast, isPending: isPendingPodcast } = useGetPodcastByIdQuery(
    { podcastId: podcastId },
    { enabled: isForPodcast }
  );
  const { data: episode, isPending: isPendingEpisode } = useGetEpisodeByIdQuery(episodeId, { enabled: isForEpisode });
  const {
    data: commentRepliesPages,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isPending,
  } = useGetInfiniteCommentReplyListQuery({ commentId: postId, page: 1, limit: 10, source });

  const {
    data: commentEpisodeRepliesPages,
    hasNextPage: hasNextPageEpisodeReply,
    isFetchingNextPage: isFetchingNextPageEpisodeReply,
    fetchNextPage: fetchNextPageEpisodeReply,
    isPending: isPendingEpisodeReply,
  } = useGetInfiniteCommentEpisodeReplyListQuery({ commentId: postId, page: 1, limit: 10, source });

  const handleRefetch = useCallback(async () => {
    if (isForEpisode) {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() }),
        queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() }),
        queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() }),
      ]);
    } else {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() }),
        queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() }),
        queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() }),
      ]);
    }
  }, [isForEpisode, queryClient]);

  const renderComment = useCallback<FlatListAnimateProps<ICommentReply>['renderItem']>(
    ({ item }) => {
      return (
        <Comment
          commentData={item}
          postInfo={post}
          podcastId={podcastId}
          episodeId={episodeId}
          onRefetch={handleRefetch}
        />
      );
    },
    [post, podcastId, episodeId, handleRefetch]
  );

  const userRate = isForPodcast ? podcastRate : episodeRate;

  const isPostOwner = post?.user?.id?.toString() === userProfile?.id?.toString();

  const commentReplies = isForEpisode
    ? (commentEpisodeRepliesPages?.pages?.map((page) => page.data).flat() ?? [])
    : (commentRepliesPages?.pages?.map((page) => page.data).flat() ?? []);
  const isHeaderLoading = isPendingPost || (isForPodcast ? isPendingPodcast : isPendingEpisode);

  const hasNextPageReply = isForEpisode ? hasNextPageEpisodeReply : hasNextPage;
  const isFetchingNextPageReply = isForEpisode ? isFetchingNextPageEpisodeReply : isFetchingNextPage;
  const fetchNextPageReply = isForEpisode ? fetchNextPageEpisodeReply : fetchNextPage;
  const isPendingReply = isForEpisode ? isPendingEpisodeReply : isPending;

  return (
    <View style={styles.container}>
      <View style={[styles.containerH, styles.headerContainer]}>
        <Header isBack title='Review Details' />
      </View>

      <FlatListAnimate
        data={commentReplies}
        style={{ flex: 1 }}
        onRefresh={handleRefetch}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <PostHeader
            postInfo={post}
            podcastInfo={isForPodcast ? podcast : undefined}
            episodeInfo={isForEpisode ? episode : undefined}
            isLoading={isHeaderLoading}
            isPostOwner={isPostOwner}
            userRate={userRate?.rate || 0}
          />
        }
        contentContainerStyle={styles.postContentBox}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderComment}
        automaticallyAdjustKeyboardInsets
        ItemSeparatorComponent={() => <Divider style={styles.divider} />}
        onEndReached={() => {
          if (hasNextPageReply && !isFetchingNextPageReply) {
            fetchNextPageReply();
          }
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={isPendingReply ? () => <CommentSkeleton /> : null}
      />

      <View style={styles.replyBox}>
        <ReplyComment postId={postId} podcastId={podcastId} episodeId={episodeId} postInfo={post} />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  divider: {
    marginVertical: 20,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
  },
  containerH: {
    paddingHorizontal: 24,
  },
  contentBox: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  headerContainer: {
    paddingVertical: 14,
    backgroundColor: theme.colors.neutralCard,
    paddingTop: rt.insets.top,
  },
  replyBox: {
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.whiteOpacity16,
  },
  postContainer: {
    flex: 1,
  },
  postContentBox: {
    paddingVertical: 28,
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  line: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.whiteOpacity20,
  },
}));
